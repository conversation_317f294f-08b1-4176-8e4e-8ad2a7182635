import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createAdmin() {
  console.log('🔄 Creating admin account...');

  try {
    // Check if admin already exists
    const existingAdmin = await prisma.adminUser.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (existingAdmin) {
      console.log('⚠️  Admin account already exists with email: <EMAIL>');
      console.log('Updating password...');
      
      const hashedPassword = await bcrypt.hash('123456', 12);
      
      const updatedAdmin = await prisma.adminUser.update({
        where: { email: '<EMAIL>' },
        data: { password: hashedPassword },
      });

      console.log('✅ Admin password updated successfully!');
      console.log(`📧 Email: ${updatedAdmin.email}`);
      console.log(`🔑 Password: 123456`);
      console.log(`👤 Name: ${updatedAdmin.name}`);
      console.log(`🛡️  Role: ${updatedAdmin.role}`);
      return;
    }

    // Create new admin account
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    const admin = await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        name: 'System Administrator',
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true,
        department: 'IT',
      },
    });

    console.log('✅ Admin account created successfully!');
    console.log('📋 Account Details:');
    console.log(`📧 Email: ${admin.email}`);
    console.log(`🔑 Password: 123456`);
    console.log(`👤 Name: ${admin.name}`);
    console.log(`🛡️  Role: ${admin.role}`);
    console.log(`🏢 Department: ${admin.department}`);
    console.log(`✅ Active: ${admin.isActive}`);
    console.log('');
    console.log('🚀 You can now login to the admin panel at: /admin/auth/signin');

  } catch (error) {
    console.error('❌ Error creating admin account:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createAdmin()
  .then(() => {
    console.log('✅ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
