import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

// GET /api/admin/products - <PERSON><PERSON><PERSON> danh sách sản phẩm cho admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can manage products
    if (session.user.role !== "ADMIN" && session.user.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Không có quyền quản lý sản phẩm" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const category = searchParams.get("category");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const featured = searchParams.get("featured");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (category) {
      where.categoryId = category;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (featured === "true") {
      where.featured = true;
    } else if (featured === "false") {
      where.featured = false;
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get admin products error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm" },
      { status: 500 }
    );
  }
}

// POST /api/admin/products - Tạo sản phẩm mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      salePrice,
      sku,
      stock,
      categoryId,
      images,
      featured,
      status,
      tags,
      slug,
    } = body;

    // Validate required fields
    if (!name || !description || !categoryId || !price) {
      return NextResponse.json(
        { error: "Thiếu thông tin bắt buộc" },
        { status: 400 }
      );
    }

    // Generate SKU if not provided
    const finalSku = sku || `PRD-${Date.now()}`;

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku: finalSku },
    });

    if (existingSku) {
      return NextResponse.json({ error: "SKU đã tồn tại" }, { status: 400 });
    }

    // Check if slug already exists
    const existingSlug = await prisma.product.findUnique({
      where: { slug },
    });

    if (existingSlug) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // Create product
    const product = await prisma.product.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        salePrice: salePrice ? parseFloat(salePrice) : null,
        sku: finalSku,
        stock: parseInt(stock) || 0,
        categoryId,
        images: images || [],
        featured: Boolean(featured),
        status: status || "ACTIVE",
        tags: tags || [],
        slug,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error("Create product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo sản phẩm" },
      { status: 500 }
    );
  }
}
