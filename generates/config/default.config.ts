export interface GeneratorConfiguration {
  users: {
    count: number;
    includeAddress: boolean;
    includeAvatar: boolean;
  };
  adminUsers: {
    count: number;
    adminRatio: number; // Ratio of ADMIN vs MODERATOR (0.0 to 1.0)
    includeAvatar: boolean;
    includeDepartments: boolean;
  };
  categories: {
    count: number;
    useDefaultCategories: boolean;
    includeImages: boolean;
  };
  products: {
    count: number;
    includeVariants: boolean;
    includeDimensions: boolean;
    salePercentage: number;
    featuredPercentage: number;
    minPrice: number;
    maxPrice: number;
    minStock: number;
    maxStock: number;
  };
  reviews: {
    count: number;
    verifiedPercentage: number;
    includeHelpfulCount: boolean;
    ratingDistribution: {
      1: number;
      2: number;
      3: number;
      4: number;
      5: number;
    };
  };
  orders: {
    count: number;
    minItems: number;
    maxItems: number;
    statusDistribution: {
      PENDING: number;
      CONFIRMED: number;
      PROCESSING: number;
      SHIPPED: number;
      DELIVERED: number;
      CANCELLED: number;
    };
    paymentMethodDistribution: {
      COD: number;
      BANK_TRANSFER: number;
      CREDIT_CARD: number;
    };
  };
  global: {
    locale: string;
    verbose: boolean;
    seed?: number;
  };
}

export const defaultConfig: GeneratorConfiguration = {
  users: {
    count: 20,
    includeAddress: true,
    includeAvatar: true,
  },
  adminUsers: {
    count: 3,
    adminRatio: 0.3, // 30% ADMIN, 70% MODERATOR
    includeAvatar: true,
    includeDepartments: true,
  },
  categories: {
    count: 0, // Will use default categories
    useDefaultCategories: true,
    includeImages: false,
  },
  products: {
    count: 50,
    includeVariants: true,
    includeDimensions: false,
    salePercentage: 30,
    featuredPercentage: 20,
    minPrice: 50000,
    maxPrice: 2000000,
    minStock: 0,
    maxStock: 100,
  },
  reviews: {
    count: 100,
    verifiedPercentage: 70,
    includeHelpfulCount: true,
    ratingDistribution: {
      1: 5,
      2: 10,
      3: 15,
      4: 35,
      5: 35,
    },
  },
  orders: {
    count: 30,
    minItems: 1,
    maxItems: 5,
    statusDistribution: {
      PENDING: 10,
      CONFIRMED: 15,
      PROCESSING: 20,
      SHIPPED: 25,
      DELIVERED: 25,
      CANCELLED: 5,
    },
    paymentMethodDistribution: {
      COD: 60,
      BANK_TRANSFER: 25,
      CREDIT_CARD: 15,
    },
  },
  global: {
    locale: "vi",
    verbose: true,
    seed: undefined, // Will use current timestamp
  },
};
