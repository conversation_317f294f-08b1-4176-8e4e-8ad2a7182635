#!/usr/bin/env node

import { Command } from "commander";
import inquirer from "inquirer";
import chalk from "chalk";
import { DataGenerator } from "../index";
import {
  defaultConfig,
  GeneratorConfiguration,
} from "../config/default.config";

const program = new Command();

program
  .name("ns-shop-generator")
  .description("NS Shop Data Generator CLI")
  .version("1.0.0");

// Generate all data with default configuration
program
  .command("generate")
  .alias("gen")
  .description("Generate all sample data with default configuration")
  .option("-v, --verbose", "Enable verbose logging", false)
  .option("-s, --seed <number>", "Set random seed for reproducible results")
  .option("--users <number>", "Number of users to generate", "20")
  .option("--products <number>", "Number of products to generate", "50")
  .option("--reviews <number>", "Number of reviews to generate", "100")
  .option("--orders <number>", "Number of orders to generate", "30")
  .action(async (options) => {
    console.log(chalk.blue("🚀 Starting NS Shop data generation..."));

    const config: Partial<GeneratorConfiguration> = {
      users: {
        ...defaultConfig.users,
        count: parseInt(options.users),
      },
      products: {
        ...defaultConfig.products,
        count: parseInt(options.products),
      },
      reviews: {
        ...defaultConfig.reviews,
        count: parseInt(options.reviews),
      },
      orders: {
        ...defaultConfig.orders,
        count: parseInt(options.orders),
      },
      global: {
        ...defaultConfig.global,
        verbose: options.verbose,
        seed: options.seed ? parseInt(options.seed) : undefined,
      },
    };

    const generator = new DataGenerator(config);

    try {
      const result = await generator.generateAll();

      if (result.success) {
        console.log(chalk.green("✅ Data generation completed successfully!"));
        console.log(chalk.gray(`Total duration: ${result.totalDuration}ms`));
      } else {
        console.log(chalk.yellow("⚠️ Data generation completed with errors:"));
        result.errors.forEach((error) => {
          console.log(chalk.red(`  - ${error}`));
        });
      }
    } catch (error) {
      console.error(chalk.red("❌ Data generation failed:"), error);
      process.exit(1);
    } finally {
      await generator.cleanup();
    }
  });

// Interactive mode
program
  .command("interactive")
  .alias("i")
  .description("Interactive mode to configure and generate data")
  .action(async () => {
    console.log(
      chalk.blue("🎯 Welcome to NS Shop Data Generator Interactive Mode")
    );

    try {
      const answers = await inquirer.prompt([
        {
          type: "confirm",
          name: "generateUsers",
          message: "Generate users?",
          default: true,
        },
        {
          type: "number",
          name: "userCount",
          message: "How many users?",
          default: 20,
          when: (answers) => answers.generateUsers,
          validate: (input) => (input && input > 0) || "Must be greater than 0",
        },
        // Admin users are now managed separately via AdminUser model
        {
          type: "confirm",
          name: "generateCategories",
          message: "Generate categories?",
          default: true,
        },
        {
          type: "confirm",
          name: "useDefaultCategories",
          message: "Use default Vietnamese fashion categories?",
          default: true,
          when: (answers) => answers.generateCategories,
        },
        {
          type: "confirm",
          name: "generateProducts",
          message: "Generate products?",
          default: true,
        },
        {
          type: "number",
          name: "productCount",
          message: "How many products?",
          default: 50,
          when: (answers) => answers.generateProducts,
          validate: (input) => (input && input > 0) || "Must be greater than 0",
        },
        {
          type: "number",
          name: "salePercentage",
          message: "Percentage of products on sale (0-100)?",
          default: 30,
          when: (answers) => answers.generateProducts,
          validate: (input) =>
            (input && input >= 0 && input <= 100) ||
            "Must be between 0 and 100",
        },
        {
          type: "confirm",
          name: "generateReviews",
          message: "Generate reviews?",
          default: true,
        },
        {
          type: "number",
          name: "reviewCount",
          message: "How many reviews?",
          default: 100,
          when: (answers) => answers.generateReviews,
          validate: (input) => (input && input > 0) || "Must be greater than 0",
        },
        {
          type: "confirm",
          name: "generateOrders",
          message: "Generate orders?",
          default: true,
        },
        {
          type: "number",
          name: "orderCount",
          message: "How many orders?",
          default: 30,
          when: (answers) => answers.generateOrders,
          validate: (input) => (input && input > 0) || "Must be greater than 0",
        },
        {
          type: "confirm",
          name: "verbose",
          message: "Enable verbose logging?",
          default: true,
        },
        {
          type: "input",
          name: "seed",
          message: "Random seed (leave empty for random):",
          validate: (input) =>
            !input || !isNaN(parseInt(input)) || "Must be a number",
        },
      ]);

      // Build configuration from answers
      const config: Partial<GeneratorConfiguration> = {
        users: {
          ...defaultConfig.users,
          count: answers.userCount || 0,
        },
        categories: {
          ...defaultConfig.categories,
          useDefaultCategories: answers.useDefaultCategories,
        },
        products: {
          ...defaultConfig.products,
          count: answers.productCount || 0,
          salePercentage: answers.salePercentage || 30,
        },
        reviews: {
          ...defaultConfig.reviews,
          count: answers.reviewCount || 0,
        },
        orders: {
          ...defaultConfig.orders,
          count: answers.orderCount || 0,
        },
        global: {
          ...defaultConfig.global,
          verbose: answers.verbose,
          seed: answers.seed ? parseInt(answers.seed) : undefined,
        },
      };

      // Set counts to 0 for disabled generators
      if (!answers.generateUsers) config.users!.count = 0;
      if (!answers.generateProducts) config.products!.count = 0;
      if (!answers.generateReviews) config.reviews!.count = 0;
      if (!answers.generateOrders) config.orders!.count = 0;

      console.log(
        chalk.blue("\n🚀 Starting data generation with your configuration...")
      );

      const generator = new DataGenerator(config);
      const result = await generator.generateAll();

      if (result.success) {
        console.log(
          chalk.green("\n✅ Data generation completed successfully!")
        );
        console.log(chalk.gray(`Total duration: ${result.totalDuration}ms`));
      } else {
        console.log(
          chalk.yellow("\n⚠️ Data generation completed with errors:")
        );
        result.errors.forEach((error) => {
          console.log(chalk.red(`  - ${error}`));
        });
      }

      await generator.cleanup();
    } catch (error) {
      console.error(chalk.red("❌ Interactive mode failed:"), error);
      process.exit(1);
    }
  });

// Individual generators
program
  .command("users")
  .description("Generate only users")
  .option("-c, --count <number>", "Number of users to generate", "20")
  // Admin users are now managed separately via AdminUser model
  .option("-v, --verbose", "Enable verbose logging", false)
  .action(async (options) => {
    const { UserGenerator } = await import("../generators/user.generator");

    const generator = new UserGenerator({
      count: parseInt(options.count),
      verbose: options.verbose,
    });

    try {
      const result = await generator.generate();
      console.log(
        result.success
          ? chalk.green(`✅ Generated ${result.count} users`)
          : chalk.red(`❌ Failed: ${result.error}`)
      );
    } finally {
      await generator.cleanup();
    }
  });

program
  .command("products")
  .description("Generate only products")
  .option("-c, --count <number>", "Number of products to generate", "50")
  .option("-v, --verbose", "Enable verbose logging", false)
  .action(async (options) => {
    const { ProductGenerator } = await import(
      "../generators/product.generator"
    );

    const generator = new ProductGenerator({
      count: parseInt(options.count),
      verbose: options.verbose,
    });

    try {
      const result = await generator.generate();
      console.log(
        result.success
          ? chalk.green(`✅ Generated ${result.count} products`)
          : chalk.red(`❌ Failed: ${result.error}`)
      );
    } finally {
      await generator.cleanup();
    }
  });

// Show configuration
program
  .command("config")
  .description("Show default configuration")
  .action(() => {
    console.log(chalk.blue("📋 Default Configuration:"));
    console.log(JSON.stringify(defaultConfig, null, 2));
  });

program.parse();
