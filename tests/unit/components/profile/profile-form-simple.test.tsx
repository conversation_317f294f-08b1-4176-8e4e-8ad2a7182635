/**
 * Profile Form Component Unit Tests (Simplified)
 * <PERSON><PERSON><PERSON> tra unit cho profile form component
 */

import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
} from "../../../helpers/test-utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { mockUsers } from "../../../fixtures/mock-data";

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(),
}));

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockPush = jest.fn();
const mockSession = useSession as jest.MockedFunction<typeof useSession>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Simple Profile Component for testing
const SimpleProfileForm = ({
  user,
  onUpdate,
}: {
  user: any;
  onUpdate: (data: any) => void;
}) => {
  const [isEditing, setIsEditing] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: user.name || "",
    phone: user.phone || "",
    dateOfBirth: user.dateOfBirth
      ? new Date(user.dateOfBirth).toISOString().split("T")[0]
      : "",
    gender: user.gender || "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      name: user.name || "",
      phone: user.phone || "",
      dateOfBirth: user.dateOfBirth
        ? new Date(user.dateOfBirth).toISOString().split("T")[0]
        : "",
      gender: user.gender || "",
    });
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <div>
        <h1>Thông tin cá nhân</h1>
        <div>
          <p>Họ tên: {user.name}</p>
          <p>Email: {user.email}</p>
          <p>Số điện thoại: {user.phone}</p>
          <p>
            Ngày sinh:{" "}
            {user.dateOfBirth
              ? new Date(user.dateOfBirth).toLocaleDateString("vi-VN")
              : "Chưa cập nhật"}
          </p>
          <p>
            Giới tính:{" "}
            {user.gender === "MALE"
              ? "Nam"
              : user.gender === "FEMALE"
              ? "Nữ"
              : user.gender === "OTHER"
              ? "Khác"
              : "Chưa cập nhật"}
          </p>
        </div>
        <button onClick={() => setIsEditing(true)}>Chỉnh sửa</button>
      </div>
    );
  }

  return (
    <div>
      <h1>Chỉnh sửa thông tin</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label>Họ tên:</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          />
        </div>
        <div>
          <label>Số điện thoại:</label>
          <input
            type="text"
            value={formData.phone}
            onChange={(e) =>
              setFormData({ ...formData, phone: e.target.value })
            }
          />
        </div>
        <div>
          <label>Ngày sinh:</label>
          <input
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) =>
              setFormData({ ...formData, dateOfBirth: e.target.value })
            }
          />
        </div>
        <div>
          <label>Giới tính:</label>
          <select
            value={formData.gender}
            onChange={(e) =>
              setFormData({ ...formData, gender: e.target.value })
            }
          >
            <option value="">Chọn giới tính</option>
            <option value="MALE">Nam</option>
            <option value="FEMALE">Nữ</option>
            <option value="OTHER">Khác</option>
          </select>
        </div>
        <button type="submit">Lưu thay đổi</button>
        <button type="button" onClick={handleCancel}>
          Hủy
        </button>
      </form>
    </div>
  );
};

describe("Profile Form Component (Simplified)", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockSession.mockReturnValue({
      data: { user: mockUsers[0] },
    } as any);

    // Mock successful fetch response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockUsers[0],
    } as Response);
  });

  describe("Profile Display", () => {
    it("should display user information", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      expect(
        screen.getByText(`Họ tên: ${mockUsers[0].name}`)
      ).toBeInTheDocument();
      expect(
        screen.getByText(`Email: ${mockUsers[0].email}`)
      ).toBeInTheDocument();
      expect(
        screen.getByText(`Số điện thoại: ${mockUsers[0].phone}`)
      ).toBeInTheDocument();
    });

    it("should display formatted date of birth", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      expect(screen.getByText(/Ngày sinh:.*15.*5.*1990/)).toBeInTheDocument();
    });

    it("should display formatted gender", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      expect(screen.getByText("Giới tính: Nam")).toBeInTheDocument();
    });

    it("should show edit button", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      (expect(editButton) as any).toBeInTheDocument();
    });
  });

  describe("Profile Editing", () => {
    it("should enter edit mode when edit button clicked", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      expect(screen.getByText("Chỉnh sửa thông tin")).toBeInTheDocument();
      expect(screen.getByDisplayValue(mockUsers[0].name)).toBeInTheDocument();
      expect(screen.getByDisplayValue(mockUsers[0].phone)).toBeInTheDocument();
    });

    it("should show save and cancel buttons in edit mode", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      expect(screen.getByText("Lưu thay đổi")).toBeInTheDocument();
      expect(screen.getByText("Hủy")).toBeInTheDocument();
    });

    it("should update form values when typing", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });

      (expect(nameInput) as any).toHaveValue("New Name");
    });

    it("should cancel editing when cancel button clicked", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Change a value
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });

      // Cancel
      const cancelButton = screen.getByText("Hủy");
      fireEvent.click(cancelButton);

      // Should exit edit mode and restore original values
      expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      expect(
        screen.getByText(`Họ tên: ${mockUsers[0].name}`)
      ).toBeInTheDocument();
    });
  });

  describe("Profile Update", () => {
    it("should submit form with updated data", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Update name
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "Updated Name" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      expect(mockUpdate).toHaveBeenCalledWith({
        name: "Updated Name",
        phone: mockUsers[0].phone,
        dateOfBirth: "1990-05-15",
        gender: mockUsers[0].gender,
      });
    });

    it("should exit edit mode after successful update", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
    });
  });

  describe("Form Validation", () => {
    it("should handle date input correctly", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const dateInput = screen.getByDisplayValue("1990-05-15");
      (expect(dateInput) as any).toHaveAttribute("type", "date");

      fireEvent.change(dateInput, { target: { value: "1995-12-25" } });
      (expect(dateInput) as any).toHaveValue("1995-12-25");
    });

    it("should handle gender selection", () => {
      const mockUpdate = jest.fn();
      render(<SimpleProfileForm user={mockUsers[0]} onUpdate={mockUpdate} />);

      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Find gender select by role
      const genderSelect = screen.getByRole("combobox");
      (expect(genderSelect) as any).toHaveValue("MALE");

      fireEvent.change(genderSelect, { target: { value: "FEMALE" } });
      (expect(genderSelect) as any).toHaveValue("FEMALE");
    });
  });
});
