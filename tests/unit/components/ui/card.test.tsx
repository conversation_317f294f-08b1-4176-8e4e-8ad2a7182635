/**
 * Card UI component unit tests
 * Kiểm tra unit cho Card UI component và các sub-components
 */

import React from "react";
import { render, screen } from "../../../helpers/test-utils";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";

describe("Card UI Components", () => {
  describe("Card", () => {
    it("should render card with default classes", () => {
      render(<Card data-testid="card">Card Content</Card>);

      const card = screen.getByTestId("card");
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass(
        "rounded-xl border bg-card text-card-foreground shadow fashion-card"
      );
    });

    it("should apply custom className", () => {
      render(
        <Card className="custom-class" data-testid="card">
          Card Content
        </Card>
      );

      const card = screen.getByTestId("card");
      expect(card).toHaveClass("custom-class");
    });

    it("should pass through additional props", () => {
      render(
        <Card id="test-card" role="article" data-testid="card">
          Card Content
        </Card>
      );

      const card = screen.getByTestId("card");
      expect(card).toHaveAttribute("id", "test-card");
      expect(card).toHaveAttribute("role", "article");
    });

    it("should render children correctly", () => {
      render(
        <Card data-testid="card">
          <div>Child Content</div>
        </Card>
      );

      expect(screen.getByText("Child Content")).toBeInTheDocument();
    });
  });

  describe("CardHeader", () => {
    it("should render header with default classes", () => {
      render(<CardHeader data-testid="card-header">Header Content</CardHeader>);

      const header = screen.getByTestId("card-header");
      expect(header).toBeInTheDocument();
      expect(header).toHaveClass("flex flex-col space-y-1.5 p-6");
    });

    it("should apply custom className", () => {
      render(
        <CardHeader className="custom-header" data-testid="card-header">
          Header Content
        </CardHeader>
      );

      const header = screen.getByTestId("card-header");
      expect(header).toHaveClass("custom-header");
    });
  });

  describe("CardTitle", () => {
    it("should render title with default classes", () => {
      render(<CardTitle data-testid="card-title">Card Title</CardTitle>);

      const title = screen.getByTestId("card-title");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass("font-semibold leading-none tracking-tight");
      expect(title).toHaveTextContent("Card Title");
    });

    it("should apply custom className", () => {
      render(
        <CardTitle className="custom-title" data-testid="card-title">
          Card Title
        </CardTitle>
      );

      const title = screen.getByTestId("card-title");
      expect(title).toHaveClass("custom-title");
    });
  });

  describe("CardDescription", () => {
    it("should render description with default classes", () => {
      render(
        <CardDescription data-testid="card-description">
          Card Description
        </CardDescription>
      );

      const description = screen.getByTestId("card-description");
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass("text-sm text-muted-foreground");
      expect(description).toHaveTextContent("Card Description");
    });

    it("should apply custom className", () => {
      render(
        <CardDescription className="custom-desc" data-testid="card-description">
          Card Description
        </CardDescription>
      );

      const description = screen.getByTestId("card-description");
      expect(description).toHaveClass("custom-desc");
    });
  });

  describe("CardContent", () => {
    it("should render content with default classes", () => {
      render(
        <CardContent data-testid="card-content">Card Content</CardContent>
      );

      const content = screen.getByTestId("card-content");
      expect(content).toBeInTheDocument();
      expect(content).toHaveClass("p-6 pt-0");
      expect(content).toHaveTextContent("Card Content");
    });

    it("should apply custom className", () => {
      render(
        <CardContent className="custom-content" data-testid="card-content">
          Card Content
        </CardContent>
      );

      const content = screen.getByTestId("card-content");
      expect(content).toHaveClass("custom-content");
    });
  });

  describe("CardFooter", () => {
    it("should render footer with default classes", () => {
      render(<CardFooter data-testid="card-footer">Footer Content</CardFooter>);

      const footer = screen.getByTestId("card-footer");
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveClass("flex items-center p-6 pt-0");
      expect(footer).toHaveTextContent("Footer Content");
    });

    it("should apply custom className", () => {
      render(
        <CardFooter className="custom-footer" data-testid="card-footer">
          Footer Content
        </CardFooter>
      );

      const footer = screen.getByTestId("card-footer");
      expect(footer).toHaveClass("custom-footer");
    });
  });

  describe("Complete Card Structure", () => {
    it("should render complete card with all components", () => {
      render(
        <Card data-testid="complete-card">
          <CardHeader data-testid="header">
            <CardTitle data-testid="title">Product Card</CardTitle>
            <CardDescription data-testid="description">
              A beautiful product description
            </CardDescription>
          </CardHeader>
          <CardContent data-testid="content">
            <p>Product details and information</p>
          </CardContent>
          <CardFooter data-testid="footer">
            <button>Add to Cart</button>
          </CardFooter>
        </Card>
      );

      // Check all components are rendered
      expect(screen.getByTestId("complete-card")).toBeInTheDocument();
      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("title")).toBeInTheDocument();
      expect(screen.getByTestId("description")).toBeInTheDocument();
      expect(screen.getByTestId("content")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();

      // Check content
      expect(screen.getByText("Product Card")).toBeInTheDocument();
      expect(
        screen.getByText("A beautiful product description")
      ).toBeInTheDocument();
      expect(
        screen.getByText("Product details and information")
      ).toBeInTheDocument();
      expect(screen.getByText("Add to Cart")).toBeInTheDocument();
    });

    it("should handle fashion card styling", () => {
      render(
        <Card
          className="group hover:shadow-lg transition-shadow"
          data-testid="fashion-card"
        >
          <CardContent>Fashion Product</CardContent>
        </Card>
      );

      const card = screen.getByTestId("fashion-card");
      expect(card).toHaveClass(
        "fashion-card group hover:shadow-lg transition-shadow"
      );
    });
  });

  describe("Accessibility", () => {
    it("should support ARIA attributes", () => {
      render(
        <Card
          role="article"
          aria-label="Product card"
          data-testid="accessible-card"
        >
          <CardHeader>
            <CardTitle>Accessible Product</CardTitle>
          </CardHeader>
        </Card>
      );

      const card = screen.getByTestId("accessible-card");
      expect(card).toHaveAttribute("role", "article");
      expect(card).toHaveAttribute("aria-label", "Product card");
    });

    it("should have proper heading structure", () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle>Main Title</CardTitle>
            <CardDescription>Subtitle</CardDescription>
          </CardHeader>
        </Card>
      );

      expect(screen.getByText("Main Title")).toBeInTheDocument();
      expect(screen.getByText("Subtitle")).toBeInTheDocument();
    });
  });
});
