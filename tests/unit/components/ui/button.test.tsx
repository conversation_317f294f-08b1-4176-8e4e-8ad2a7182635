/**
 * Button UI component unit tests
 * <PERSON><PERSON><PERSON> tra unit cho Button UI component
 */

import React from "react";
import { render, screen, fireEvent } from "../../../helpers/test-utils";
import { Button } from "@/components/ui/button";

describe("Button UI Component", () => {
  describe("Rendering", () => {
    it("should render button with text", () => {
      render(<Button>Click me</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent("Click me");
    });

    it("should render button with default variant", () => {
      render(<Button>Default Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-primary text-primary-foreground");
    });

    it("should render button with secondary variant", () => {
      render(<Button variant="secondary">Secondary Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-secondary text-secondary-foreground");
    });

    it("should render button with outline variant", () => {
      render(<Button variant="outline">Outline Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("border border-input bg-background");
    });

    it("should render button with ghost variant", () => {
      render(<Button variant="ghost">Ghost Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "hover:bg-accent hover:text-accent-foreground"
      );
    });

    it("should render button with destructive variant", () => {
      render(<Button variant="destructive">Delete</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-destructive text-destructive-foreground");
    });
  });

  describe("Sizes", () => {
    it("should render button with default size", () => {
      render(<Button>Default Size</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("h-9 px-4 py-2");
    });

    it("should render button with small size", () => {
      render(<Button size="sm">Small Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("h-8 rounded-md px-3 text-xs");
    });

    it("should render button with large size", () => {
      render(<Button size="lg">Large Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("h-10 rounded-md px-8");
    });

    it("should render icon button", () => {
      render(<Button size="icon">🔍</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("h-9 w-9");
    });
  });

  describe("Fashion Variants", () => {
    it("should render button with fashion variant", () => {
      render(<Button variant="fashion">Fashion Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "bg-gradient-to-r from-fashion-500 to-fashion-600 text-white shadow-lg"
      );
    });

    it("should render button with luxury variant", () => {
      render(<Button variant="luxury">Luxury Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "bg-gradient-to-r from-luxury-800 to-luxury-900 text-white shadow-xl"
      );
    });

    it("should render button with link variant", () => {
      render(<Button variant="link">Link Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "text-primary underline-offset-4 hover:underline"
      );
    });
  });

  describe("States", () => {
    it("should be disabled when disabled prop is true", () => {
      render(<Button disabled>Disabled Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
      expect(button).toHaveClass(
        "disabled:pointer-events-none disabled:opacity-50"
      );
    });

    it("should not call onClick when disabled", () => {
      const handleClick = jest.fn();
      render(
        <Button disabled onClick={handleClick}>
          Disabled Button
        </Button>
      );

      const button = screen.getByRole("button");
      fireEvent.click(button);

      expect(handleClick).not.toHaveBeenCalled();
    });

    it("should handle loading state", () => {
      render(<Button disabled>Loading...</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
    });
  });

  describe("Interactions", () => {
    it("should handle click events", () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Click me</Button>);

      const button = screen.getByRole("button");
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("should handle multiple clicks", () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Click me</Button>);

      const button = screen.getByRole("button");
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(3);
    });

    it("should handle keyboard events", () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Press me</Button>);

      const button = screen.getByRole("button");
      button.focus();

      // For this test, we'll just verify the button is focusable
      // Keyboard events are handled by the browser, not our component
      expect(document.activeElement).toBe(button);
    });
  });

  describe("Custom Props", () => {
    it("should apply custom className", () => {
      render(<Button className="custom-class">Custom Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass("custom-class");
    });

    it("should pass through HTML attributes", () => {
      render(
        <Button type="submit" id="submit-btn" data-testid="submit">
          Submit
        </Button>
      );

      const button = screen.getByRole("button");
      expect(button).toHaveAttribute("type", "submit");
      expect(button).toHaveAttribute("id", "submit-btn");
      expect(button).toHaveAttribute("data-testid", "submit");
    });

    it("should render as different element when asChild is used", () => {
      // This test would need the actual implementation details
      // For now, just test that the component renders
      render(<Button>As Child Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should have proper button role", () => {
      render(<Button>Accessible Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("should be focusable", () => {
      render(<Button>Focusable Button</Button>);

      const button = screen.getByRole("button");
      button.focus();
      expect(document.activeElement).toBe(button);
    });

    it("should not be focusable when disabled", () => {
      render(<Button disabled>Disabled Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toBeDisabled();

      // Disabled buttons should not be focusable
      button.focus();
      expect(document.activeElement).not.toBe(button);
    });

    it("should have accessible name", () => {
      render(<Button>Accessible Button</Button>);

      const button = screen.getByRole("button", { name: "Accessible Button" });
      expect(button).toBeInTheDocument();
    });
  });

  describe("Styling", () => {
    it("should have base button classes", () => {
      render(<Button>Styled Button</Button>);

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
      );
    });

    it("should have hover and focus states", () => {
      render(<Button>Interactive Button</Button>);

      const button = screen.getByRole("button");

      // Hover state
      fireEvent.mouseEnter(button);
      expect(button).toHaveClass("hover:bg-primary/90");

      // Focus state
      button.focus();
      expect(button).toHaveClass("focus-visible:ring-1");
    });
  });
});
