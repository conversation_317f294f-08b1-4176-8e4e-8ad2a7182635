/**
 * Header component unit tests
 * <PERSON><PERSON><PERSON> tra unit cho Header component
 */

import React from "react";
import { render, screen, fireEvent } from "../../../helpers/test-utils";
import { Header } from "@/components/layout/header";

// Mock the Button component
jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, className, variant, size, ...props }: any) => (
    <button
      onClick={onClick}
      className={className}
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

describe("Header Component", () => {
  beforeEach(() => {
    // Reset any mocks before each test
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render header with logo and navigation", () => {
      render(<Header />);

      // Check logo
      const logo = screen.getByText("NS Shop");
      (expect(logo) as any).toBeInTheDocument();

      // Check navigation links
      expect(screen.getByText("Trang chủ")).toBeInTheDocument();
      expect(screen.getByText("Sản phẩm")).toBeInTheDocument();
      expect(screen.getByText("Danh mục")).toBeInTheDocument();
      expect(screen.getByText("Về chúng tôi")).toBeInTheDocument();
      expect(screen.getByText("Liên hệ")).toBeInTheDocument();
    });

    it("should render action buttons", () => {
      render(<Header />);

      // Check for action buttons by their icons or roles
      const buttons = screen.getAllByRole("button");
      expect(buttons.length).toBeGreaterThan(0);

      // Check for cart button with badge
      const cartBadge = screen.getByText("0");
      (expect(cartBadge) as any).toBeInTheDocument();
    });

    it("should render search input on desktop", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      (expect(searchInput) as any).toBeInTheDocument();
    });

    it("should have proper header structure", () => {
      render(<Header />);

      const header = screen.getByRole("banner");
      (expect(header) as any).toBeInTheDocument();
      (expect(header) as any).toHaveClass("sticky top-0 z-50");
    });
  });

  describe("Mobile Menu Functionality", () => {
    it("should toggle mobile menu when menu button is clicked", () => {
      render(<Header />);

      // Find menu button (should be hidden on desktop, visible on mobile)
      const menuButtons = screen.getAllByRole("button");
      const menuButton = menuButtons.find(
        (button) => button.querySelector("svg") // Menu icon
      );

      if (menuButton) {
        // Initially mobile menu should be closed
        expect(screen.queryByRole("navigation")).toBeInTheDocument();

        // Click menu button
        fireEvent.click(menuButton);

        // Mobile menu should still be present (it's always in DOM, just hidden/shown with CSS)
        expect(screen.getByRole("navigation")).toBeInTheDocument();
      }
    });

    it("should close mobile menu when navigation link is clicked", () => {
      render(<Header />);

      // Find and click a navigation link
      const homeLink = screen.getByText("Trang chủ");
      fireEvent.click(homeLink);

      // Menu should still be in DOM (CSS controls visibility)
      expect(screen.getByRole("navigation")).toBeInTheDocument();
    });
  });

  describe("Search Functionality", () => {
    it("should render search input with proper attributes", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      (expect(searchInput) as any).toHaveAttribute("type", "text");
      (expect(searchInput) as any).toHaveClass("w-full");
    });

    it("should handle search input changes", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "áo thun" } });
      (expect(searchInput) as any).toHaveValue("áo thun");
    });
  });

  describe("Navigation Links", () => {
    it("should render all navigation links with correct hrefs", () => {
      render(<Header />);

      const homeLink = screen.getByRole("link", { name: /trang chủ/i });
      (expect(homeLink) as any).toHaveAttribute("href", "/");

      const productsLink = screen.getByRole("link", { name: /sản phẩm/i });
      (expect(productsLink) as any).toHaveAttribute("href", "/products");

      const categoriesLink = screen.getByRole("link", { name: /danh mục/i });
      (expect(categoriesLink) as any).toHaveAttribute("href", "/categories");

      const aboutLink = screen.getByRole("link", { name: /về chúng tôi/i });
      (expect(aboutLink) as any).toHaveAttribute("href", "/about");

      const contactLink = screen.getByRole("link", { name: /liên hệ/i });
      (expect(contactLink) as any).toHaveAttribute("href", "/contact");
    });

    it("should have logo link pointing to home", () => {
      render(<Header />);

      const logoLink = screen.getByRole("link", { name: /ns shop/i });
      (expect(logoLink) as any).toHaveAttribute("href", "/");
    });
  });

  describe("Action Buttons", () => {
    it("should render cart button with item count", () => {
      render(<Header />);

      const cartBadge = screen.getByText("0");
      (expect(cartBadge) as any).toBeInTheDocument();
      (expect(cartBadge) as any).toHaveClass("absolute -top-1 -right-1");
    });

    it("should render user and wishlist buttons on desktop", () => {
      render(<Header />);

      // These buttons should be present but may be hidden on mobile
      const buttons = screen.getAllByRole("button");
      expect(buttons.length).toBeGreaterThan(3); // Menu, search, cart, user, wishlist
    });
  });

  describe("Responsive Behavior", () => {
    it("should have responsive classes for mobile/desktop", () => {
      render(<Header />);

      // Check for responsive classes
      const header = screen.getByRole("banner");
      (expect(header) as any).toBeInTheDocument();

      // Navigation should have responsive classes
      const navigation = screen.getByRole("navigation");
      (expect(navigation) as any).toBeInTheDocument();
    });

    it("should show/hide elements based on screen size classes", () => {
      render(<Header />);

      // Check for mobile-specific and desktop-specific classes
      const buttons = screen.getAllByRole("button");

      // Some buttons should have md:hidden or sm:flex classes
      buttons.forEach((button) => {
        const className = button.className;
        // Just verify the classes exist, actual responsive behavior is handled by CSS
        expect(typeof className).toBe("string");
      });
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA labels and roles", () => {
      render(<Header />);

      const header = screen.getByRole("banner");
      (expect(header) as any).toBeInTheDocument();

      const navigation = screen.getByRole("navigation");
      (expect(navigation) as any).toBeInTheDocument();

      const links = screen.getAllByRole("link");
      expect(links.length).toBeGreaterThan(0);

      const buttons = screen.getAllByRole("button");
      expect(buttons.length).toBeGreaterThan(0);
    });

    it("should have keyboard navigation support", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      // Focus on search input
      searchInput.focus();
      expect(document.activeElement).toBe(searchInput);
    });
  });
});
