/**
 * SignUp Form component unit tests
 * <PERSON><PERSON><PERSON> tra unit cho SignUp form component
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../../../helpers/test-utils";
import { useRouter } from "next/navigation";

// Mock Next.js router
const mockPush = jest.fn();
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

// Mock SignUp component (simplified version for testing)
const SignUpForm = () => {
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Handle success
      } else {
        // Handle error
      }
    } catch (error) {
      // Handle error
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} data-testid="signup-form">
      <div>
        <label htmlFor="name">Họ và tên</label>
        <input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
          data-testid="name-input"
        />
      </div>

      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          required
          data-testid="email-input"
        />
      </div>

      <div>
        <label htmlFor="password">Mật khẩu</label>
        <input
          id="password"
          type={showPassword ? "text" : "password"}
          value={formData.password}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          required
          data-testid="password-input"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          data-testid="toggle-password"
        >
          {showPassword ? "Hide" : "Show"}
        </button>
      </div>

      <div>
        <label htmlFor="confirmPassword">Xác nhận mật khẩu</label>
        <input
          id="confirmPassword"
          type={showConfirmPassword ? "text" : "password"}
          value={formData.confirmPassword}
          onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
          required
          data-testid="confirm-password-input"
        />
        <button
          type="button"
          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          data-testid="toggle-confirm-password"
        >
          {showConfirmPassword ? "Hide" : "Show"}
        </button>
      </div>

      <div>
        <input
          type="checkbox"
          id="terms"
          required
          data-testid="terms-checkbox"
        />
        <label htmlFor="terms">
          Tôi đồng ý với Điều khoản dịch vụ và Chính sách bảo mật
        </label>
      </div>

      <button type="submit" disabled={isLoading} data-testid="submit-button">
        {isLoading ? "Đang đăng ký..." : "Đăng ký"}
      </button>
    </form>
  );
};

describe("SignUp Form Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe("Rendering", () => {
    it("should render signup form with all fields", () => {
      render(<SignUpForm />);

      expect(screen.getByTestId("signup-form")).toBeInTheDocument();
      expect(screen.getByTestId("name-input")).toBeInTheDocument();
      expect(screen.getByTestId("email-input")).toBeInTheDocument();
      expect(screen.getByTestId("password-input")).toBeInTheDocument();
      expect(screen.getByTestId("confirm-password-input")).toBeInTheDocument();
      expect(screen.getByTestId("terms-checkbox")).toBeInTheDocument();
      expect(screen.getByTestId("submit-button")).toBeInTheDocument();
    });

    it("should have proper form labels", () => {
      render(<SignUpForm />);

      expect(screen.getByLabelText("Họ và tên")).toBeInTheDocument();
      expect(screen.getByLabelText("Email")).toBeInTheDocument();
      expect(screen.getByLabelText("Mật khẩu")).toBeInTheDocument();
      expect(screen.getByLabelText("Xác nhận mật khẩu")).toBeInTheDocument();
    });

    it("should have required attributes on inputs", () => {
      render(<SignUpForm />);

      const nameInput = screen.getByTestId("name-input");
      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");
      const confirmPasswordInput = screen.getByTestId("confirm-password-input");
      const termsCheckbox = screen.getByTestId("terms-checkbox");

      (expect(nameInput) as any).toHaveAttribute("required");
      (expect(emailInput) as any).toHaveAttribute("required");
      (expect(passwordInput) as any).toHaveAttribute("required");
      (expect(confirmPasswordInput) as any).toHaveAttribute("required");
      (expect(termsCheckbox) as any).toHaveAttribute("required");
    });
  });

  describe("Form Interactions", () => {
    it("should update all input values", () => {
      render(<SignUpForm />);

      const nameInput = screen.getByTestId("name-input");
      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");
      const confirmPasswordInput = screen.getByTestId("confirm-password-input");

      fireEvent.change(nameInput, { target: { value: "John Doe" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });
      fireEvent.change(confirmPasswordInput, { target: { value: "password123" } });

      (expect(nameInput) as any).toHaveValue("John Doe");
      (expect(emailInput) as any).toHaveValue("<EMAIL>");
      (expect(passwordInput) as any).toHaveValue("password123");
      (expect(confirmPasswordInput) as any).toHaveValue("password123");
    });

    it("should toggle password visibility", () => {
      render(<SignUpForm />);

      const passwordInput = screen.getByTestId("password-input");
      const toggleButton = screen.getByTestId("toggle-password");

      (expect(passwordInput) as any).toHaveAttribute("type", "password");
      fireEvent.click(toggleButton);
      (expect(passwordInput) as any).toHaveAttribute("type", "text");
    });

    it("should toggle confirm password visibility", () => {
      render(<SignUpForm />);

      const confirmPasswordInput = screen.getByTestId("confirm-password-input");
      const toggleButton = screen.getByTestId("toggle-confirm-password");

      (expect(confirmPasswordInput) as any).toHaveAttribute("type", "password");
      fireEvent.click(toggleButton);
      (expect(confirmPasswordInput) as any).toHaveAttribute("type", "text");
    });

    it("should handle terms checkbox", () => {
      render(<SignUpForm />);

      const termsCheckbox = screen.getByTestId("terms-checkbox");

      (expect(termsCheckbox) as any).not.toBeChecked();
      fireEvent.click(termsCheckbox);
      (expect(termsCheckbox) as any).toBeChecked();
    });
  });

  describe("Form Submission", () => {
    it("should call API on successful form submission", async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ message: "Success" }),
      });

      render(<SignUpForm />);

      // Fill form
      fireEvent.change(screen.getByTestId("name-input"), { target: { value: "John Doe" } });
      fireEvent.change(screen.getByTestId("email-input"), { target: { value: "<EMAIL>" } });
      fireEvent.change(screen.getByTestId("password-input"), { target: { value: "password123" } });
      fireEvent.change(screen.getByTestId("confirm-password-input"), { target: { value: "password123" } });
      fireEvent.click(screen.getByTestId("terms-checkbox"));

      // Submit form
      fireEvent.click(screen.getByTestId("submit-button"));

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith("/api/auth/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: "John Doe",
            email: "<EMAIL>",
            password: "password123",
          }),
        });
      });
    });

    it("should show loading state during submission", async () => {
      (global.fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );

      render(<SignUpForm />);

      // Fill form
      fireEvent.change(screen.getByTestId("name-input"), { target: { value: "John Doe" } });
      fireEvent.change(screen.getByTestId("email-input"), { target: { value: "<EMAIL>" } });
      fireEvent.change(screen.getByTestId("password-input"), { target: { value: "password123" } });
      fireEvent.change(screen.getByTestId("confirm-password-input"), { target: { value: "password123" } });
      fireEvent.click(screen.getByTestId("terms-checkbox"));

      const submitButton = screen.getByTestId("submit-button");

      // Submit form
      fireEvent.click(submitButton);

      // Check loading state
      (expect(submitButton) as any).toHaveTextContent("Đang đăng ký...");
      (expect(submitButton) as any).toBeDisabled();
    });

    it("should prevent submission when passwords don't match", async () => {
      render(<SignUpForm />);

      // Fill form with mismatched passwords
      fireEvent.change(screen.getByTestId("name-input"), { target: { value: "John Doe" } });
      fireEvent.change(screen.getByTestId("email-input"), { target: { value: "<EMAIL>" } });
      fireEvent.change(screen.getByTestId("password-input"), { target: { value: "password123" } });
      fireEvent.change(screen.getByTestId("confirm-password-input"), { target: { value: "different" } });
      fireEvent.click(screen.getByTestId("terms-checkbox"));

      // Submit form
      fireEvent.click(screen.getByTestId("submit-button"));

      // API should not be called
      expect(global.fetch).not.toHaveBeenCalled();
    });
  });

  describe("Validation", () => {
    it("should require all fields", () => {
      render(<SignUpForm />);

      expect(screen.getByTestId("name-input")).toBeRequired();
      expect(screen.getByTestId("email-input")).toBeRequired();
      expect(screen.getByTestId("password-input")).toBeRequired();
      expect(screen.getByTestId("confirm-password-input")).toBeRequired();
      expect(screen.getByTestId("terms-checkbox")).toBeRequired();
    });

    it("should validate email format", () => {
      render(<SignUpForm />);

      const emailInput = screen.getByTestId("email-input");
      (expect(emailInput) as any).toHaveAttribute("type", "email");
    });
  });
});
