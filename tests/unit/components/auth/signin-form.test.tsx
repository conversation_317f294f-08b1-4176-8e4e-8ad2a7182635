/**
 * SignIn Form component unit tests
 * <PERSON><PERSON><PERSON> tra unit cho SignIn form component
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../../../helpers/test-utils";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";

// Mock Next.js router
const mockPush = jest.fn();
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

// Mock NextAuth
jest.mock("next-auth/react", () => ({
  signIn: jest.fn(),
  getSession: jest.fn(),
}));

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

// Mock SignIn component (simplified version for testing)
const SignInForm = () => {
  const [formData, setFormData] = React.useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        // Handle error
      } else {
        // Handle success
      }
    } catch (error) {
      // Handle error
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} data-testid="signin-form">
      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          required
          data-testid="email-input"
        />
      </div>

      <div>
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type={showPassword ? "text" : "password"}
          value={formData.password}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          required
          data-testid="password-input"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          data-testid="toggle-password"
        >
          {showPassword ? "Hide" : "Show"}
        </button>
      </div>

      <button type="submit" disabled={isLoading} data-testid="submit-button">
        {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
      </button>
    </form>
  );
};

describe("SignIn Form Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render signin form with all fields", () => {
      render(<SignInForm />);

      expect(screen.getByTestId("signin-form")).toBeInTheDocument();
      expect(screen.getByTestId("email-input")).toBeInTheDocument();
      expect(screen.getByTestId("password-input")).toBeInTheDocument();
      expect(screen.getByTestId("submit-button")).toBeInTheDocument();
      expect(screen.getByTestId("toggle-password")).toBeInTheDocument();
    });

    it("should have proper form labels", () => {
      render(<SignInForm />);

      expect(screen.getByLabelText("Email")).toBeInTheDocument();
      expect(screen.getByLabelText("Password")).toBeInTheDocument();
    });

    it("should have required attributes on inputs", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");

      (expect(emailInput) as any).toHaveAttribute("required");
      (expect(passwordInput) as any).toHaveAttribute("required");
      (expect(emailInput) as any).toHaveAttribute("type", "email");
    });
  });

  describe("Form Interactions", () => {
    it("should update email input value", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      (expect(emailInput) as any).toHaveValue("<EMAIL>");
    });

    it("should update password input value", () => {
      render(<SignInForm />);

      const passwordInput = screen.getByTestId("password-input");
      fireEvent.change(passwordInput, { target: { value: "password123" } });

      (expect(passwordInput) as any).toHaveValue("password123");
    });

    it("should toggle password visibility", () => {
      render(<SignInForm />);

      const passwordInput = screen.getByTestId("password-input");
      const toggleButton = screen.getByTestId("toggle-password");

      // Initially password should be hidden
      (expect(passwordInput) as any).toHaveAttribute("type", "password");
      (expect(toggleButton) as any).toHaveTextContent("Show");

      // Click to show password
      fireEvent.click(toggleButton);
      (expect(passwordInput) as any).toHaveAttribute("type", "text");
      (expect(toggleButton) as any).toHaveTextContent("Hide");

      // Click to hide password again
      fireEvent.click(toggleButton);
      (expect(passwordInput) as any).toHaveAttribute("type", "password");
      (expect(toggleButton) as any).toHaveTextContent("Show");
    });
  });

  describe("Form Submission", () => {
    it("should call signIn on form submission", async () => {
      const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;
      mockSignIn.mockResolvedValue({ ok: true, error: null } as any);

      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");
      const submitButton = screen.getByTestId("submit-button");

      // Fill form
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });

      // Submit form
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith("credentials", {
          email: "<EMAIL>",
          password: "password123",
          redirect: false,
        });
      });
    });

    it("should show loading state during submission", async () => {
      const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;
      mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");
      const submitButton = screen.getByTestId("submit-button");

      // Fill form
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });

      // Submit form
      fireEvent.click(submitButton);

      // Check loading state
      (expect(submitButton) as any).toHaveTextContent("Đang đăng nhập...");
      (expect(submitButton) as any).toBeDisabled();

      // Wait for completion
      await waitFor(() => {
        (expect(submitButton) as any).toHaveTextContent("Đăng nhập");
        (expect(submitButton) as any).not.toBeDisabled();
      });
    });

    it("should prevent submission with empty fields", () => {
      render(<SignInForm />);

      const form = screen.getByTestId("signin-form");
      const submitButton = screen.getByTestId("submit-button");

      // Try to submit empty form
      fireEvent.click(submitButton);

      // Form should not be submitted due to HTML5 validation
      expect(signIn).not.toHaveBeenCalled();
    });
  });

  describe("Validation", () => {
    it("should validate email format", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      
      // Invalid email
      fireEvent.change(emailInput, { target: { value: "invalid-email" } });
      fireEvent.blur(emailInput);

      // HTML5 validation should handle this
      (expect(emailInput) as any).toHaveValue("invalid-email");
    });

    it("should require both email and password", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");

      expect(emailInput).toBeRequired();
      expect(passwordInput).toBeRequired();
    });
  });

  describe("Accessibility", () => {
    it("should have proper form accessibility", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");

      // Check labels are properly associated
      expect(emailInput).toHaveAccessibleName("Email");
      expect(passwordInput).toHaveAccessibleName("Password");
    });

    it("should support keyboard navigation", () => {
      render(<SignInForm />);

      const emailInput = screen.getByTestId("email-input");
      const passwordInput = screen.getByTestId("password-input");
      const submitButton = screen.getByTestId("submit-button");

      // Tab order should be logical
      emailInput.focus();
      expect(document.activeElement).toBe(emailInput);

      fireEvent.keyDown(emailInput, { key: "Tab" });
      // Note: jsdom doesn't handle tab navigation automatically
      // In real browser, focus would move to password input
    });
  });
});
