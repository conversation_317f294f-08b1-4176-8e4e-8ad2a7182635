/**
 * Search Flow Integration Tests (Simplified)
 * Kiểm tra integration cho toàn bộ flow search từ input đến hiển thị kết quả
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../helpers/test-utils";
import { useRouter } from "next/navigation";
import { Header } from "@/components/layout/header";
import { mockProducts, mockApiResponses } from "../fixtures/mock-data";

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(() => ({ data: null })),
}));

// Mock useSettingsContext
jest.mock("@/contexts/SettingsContext", () => ({
  useSettingsContext: jest.fn(() => ({
    settings: {
      siteName: "NS Shop",
    },
  })),
}));

// Mock fetch
global.fetch = jest.fn();

const mockPush = jest.fn();
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe("Search Flow Integration (Simplified)", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    // Mock successful fetch response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockApiResponses.searchSuccess,
    } as Response);
  });

  describe("Basic Search Flow", () => {
    it("should navigate to search page when submitting search form", async () => {
      render(<Header />);

      // Find search input
      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      // Type search query
      fireEvent.change(searchInput, { target: { value: "áo thun" } });
      
      // Submit form
      fireEvent.submit(searchInput.closest("form")!);

      // Verify navigation was called
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20thun");
      });
    });

    it("should clear search input after submit", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      fireEvent.change(searchInput, { target: { value: "test query" } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        (expect(searchInput) as any).toHaveValue("");
      });
    });

    it("should not navigate with empty search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      // Submit without typing anything
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });
    });

    it("should encode special characters in URL", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      fireEvent.change(searchInput, { target: { value: "áo & quần" } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20%26%20qu%E1%BA%A7n");
      });
    });
  });

  describe("Mobile Search Flow", () => {
    it("should handle mobile search navigation", async () => {
      render(<Header />);

      // Open mobile menu
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(button => 
        button.classList.contains("md:hidden") && 
        button.querySelector('svg.lucide-menu')
      );
      
      fireEvent.click(menuButton!);

      // Find mobile search elements
      const searchButton = screen.getByText("Tìm");
      const mobileSearchInputs = screen.getAllByPlaceholderText("Tìm kiếm sản phẩm...");
      const mobileSearchInput = mobileSearchInputs[1]; // Second one is mobile

      // Perform search
      fireEvent.change(mobileSearchInput, { target: { value: "quần jeans" } });
      fireEvent.click(searchButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=qu%E1%BA%A7n%20jeans");
      });
    });

    it("should handle Enter key in mobile search", async () => {
      render(<Header />);

      // Open mobile menu
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(button => 
        button.classList.contains("md:hidden") && 
        button.querySelector('svg.lucide-menu')
      );
      
      fireEvent.click(menuButton!);

      // Find mobile search input
      const mobileSearchInputs = screen.getAllByPlaceholderText("Tìm kiếm sản phẩm...");
      const mobileSearchInput = mobileSearchInputs[1];

      fireEvent.change(mobileSearchInput, { target: { value: "áo sơ mi" } });
      fireEvent.keyDown(mobileSearchInput, { key: "Enter", code: "Enter" });

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20s%C6%A1%20mi");
      });
    });
  });

  describe("Search Input Validation", () => {
    it("should trim whitespace from search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      fireEvent.change(searchInput, { target: { value: "  áo thun  " } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20thun");
      });
    });

    it("should handle very long search queries", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      const longQuery = "a".repeat(200);
      
      fireEvent.change(searchInput, { target: { value: longQuery } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(`/search?search=${encodeURIComponent(longQuery)}`);
      });
    });

    it("should handle special characters and emojis", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      const specialQuery = "áo 👕 & quần 👖";
      
      fireEvent.change(searchInput, { target: { value: specialQuery } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(`/search?search=${encodeURIComponent(specialQuery)}`);
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle network errors gracefully", async () => {
      // Mock network error
      mockFetch.mockRejectedValue(new Error("Network error"));

      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      fireEvent.change(searchInput, { target: { value: "test" } });
      fireEvent.submit(searchInput.closest("form")!);

      // Should still navigate even if there's a network error
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=test");
      });
    });

    it("should handle API errors gracefully", async () => {
      // Mock API error
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        json: async () => ({ error: "Internal server error" }),
      } as Response);

      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      fireEvent.change(searchInput, { target: { value: "test" } });
      fireEvent.submit(searchInput.closest("form")!);

      // Should still navigate even if API returns error
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=test");
      });
    });
  });

  describe("Performance", () => {
    it("should not make multiple navigation calls for same query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      // Submit same query multiple times quickly
      fireEvent.change(searchInput, { target: { value: "test" } });
      fireEvent.submit(searchInput.closest("form")!);
      fireEvent.submit(searchInput.closest("form")!);
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        // Should only navigate once (because input gets cleared after first submit)
        expect(mockPush).toHaveBeenCalledTimes(1);
      });
    });

    it("should handle rapid input changes", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      
      // Type multiple characters quickly
      fireEvent.change(searchInput, { target: { value: "a" } });
      fireEvent.change(searchInput, { target: { value: "ao" } });
      fireEvent.change(searchInput, { target: { value: "áo" } });
      
      // Submit final value
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o");
      });
    });
  });
});
