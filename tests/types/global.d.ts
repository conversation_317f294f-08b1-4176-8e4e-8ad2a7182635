import { jest } from "@jest/globals";
import { NextRequest, NextResponse } from "next/server";

// Define NextFetchEvent type for middleware tests
export interface NextFetchEvent {
  waitUntil(promise: Promise<any>): void;
  passThroughOnException(): void;
}

// Define NextMiddlewareResult type
export type NextMiddlewareResult = NextResponse | Response | null | undefined;

declare global {
  // Extend global fetch to be compatible with Jest mocks
  var fetch: jest.MockedFunction<typeof globalThis.fetch>;

  namespace NodeJS {
    interface Global {
      fetch: jest.MockedFunction<typeof globalThis.fetch>;
    }
  }
}
