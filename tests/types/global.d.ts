/// <reference types="@testing-library/jest-dom" />
import { jest } from "@jest/globals";
import { NextRequest, NextResponse } from "next/server";
import "@testing-library/jest-dom";

// Define NextFetchEvent type for middleware tests
export interface NextFetchEvent {
  waitUntil(promise: Promise<any>): void;
  passThroughOnException(): void;
}

// Define NextMiddlewareResult type
export type NextMiddlewareResult = NextResponse | Response | null | undefined;

declare global {
  // Extend global fetch to be compatible with Jest mocks
  var fetch: jest.MockedFunction<typeof fetch> | typeof fetch;

  namespace NodeJS {
    interface Global {
      fetch: jest.MockedFunction<typeof fetch> | typeof fetch;
    }
  }

  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeChecked(): R;
      toHaveClass(className: string): R;
      toHaveTextContent(text: string | RegExp): R;
      toHaveValue(value: string | number): R;
      toHaveDisplayValue(value: string | RegExp): R;
      toBeInvalid(): R;
      toBeValid(): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveStyle(css: string | Record<string, any>): R;
      toHaveFocus(): R;
      toBeEmptyDOMElement(): R;
      toContainElement(element: HTMLElement | null): R;
      toContainHTML(htmlText: string): R;
      toHaveDescription(text?: string | RegExp): R;
      toHaveErrorMessage(text?: string | RegExp): R;
    }
  }
}
